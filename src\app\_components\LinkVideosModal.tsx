"use client";

import { useState, useEffect } from "react";
import { useParams } from "next/navigation";
import { toast } from "react-hot-toast";
import { Button } from "~/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON>Tit<PERSON>,
  Di<PERSON>Trigger,
} from "~/components/ui/dialog";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { api } from "~/trpc/react";
import { MultiSelect } from "./MultiSelect";
import { formatString } from "~/lib/utils";
import { Link } from "lucide-react";

interface Video {
  id: string;
  title: string;
  filename: string;
}

interface VideoRelationship {
  video: Video;
  relationType: string;
}

interface LinkVideosModalProps {
  trigger?: React.ReactNode;
  disabled?: boolean;
}

export const LinkVideosModal = ({
  trigger,
  disabled,
}: LinkVideosModalProps) => {
  const [open, setOpen] = useState(false);
  const [selectedVideos, setSelectedVideos] = useState<VideoRelationship[]>([]);
  const params = useParams();
  const parentId = params.vid as string;

  // Get the current video's sport
  const { data: currentVideoSport } = api.video.getVideoSport.useQuery(
    { videoId: parentId },
    { enabled: !!parentId },
  );

  // Get videos by sport (excluding the current video)
  const { data: videosBySport = [], isFetching: isLoadingVideosBySport } =
    api.video.getVideosBySport.useQuery(
      { sport: currentVideoSport! },
      { enabled: !!currentVideoSport },
    );

  // Get already linked videos
  const { data: linkedVideosData } = api.video.getLinkedVideos.useQuery(
    {
      parentId: parentId,
    },
    { enabled: !!parentId },
  );

  const linkedVideos = linkedVideosData?.videoList ?? [];

  // Filter out the current video from the lister types
  const availableVideos = videosBySport
    .filter((video) => video.id !== parentId)
    .map((video) => ({
      id: video.id,
      title: video.title ?? "",
      filename: video.filename ?? "",
    }));

  const initializeSelectedVideos = () => {
    const linkedVideoRelationships = linkedVideos.map((video) => ({
      video: {
        id: video.id,
        title: video.title ?? "",
        filename: video.filename ?? "",
      },
      relationType: video.relationshipType ?? "",
    }));
    setSelectedVideos(linkedVideoRelationships);
  };

  useEffect(() => {
    if (linkedVideos.length > 0 && selectedVideos.length === 0) {
      initializeSelectedVideos();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [linkedVideos]);

  const { mutate: createRelationship, isPending } =
    api.video.createVideoRelationship.useMutation({
      onSuccess: () => {
        toast.success("Videos linked successfully");
        setOpen(false);
        setSelectedVideos([]);
      },
      onError: (error) => {
        toast.error(error.message || "Failed to link videos");
      },
    });

  const handleVideoSelection = (videos: Video[]) => {
    // Keep existing relationships for videos that are still selected
    const existingRelationships = selectedVideos.filter((sv) =>
      videos.some((v) => v.id === sv.video.id),
    );

    // Add new videos with empty relation types
    const newVideos = videos
      .filter((video) => !selectedVideos.some((sv) => sv.video.id === video.id))
      .map((video) => ({
        video,
        relationType: "",
      }));

    setSelectedVideos([...existingRelationships, ...newVideos]);
  };

  const handleRelationTypeChange = (videoId: string, relationType: string) => {
    setSelectedVideos((prev) =>
      prev.map((item) =>
        item.video.id === videoId ? { ...item, relationType } : item,
      ),
    );
  };

  const onSubmit = () => {
    if (selectedVideos.length === 0) {
      toast.error("Please select at least one video to link");
      return;
    }

    // Validate that all selected videos have relation types
    const invalidVideos = selectedVideos.filter((v) => !v.relationType.trim());
    if (invalidVideos.length > 0) {
      toast.error("Please enter relationship types for all selected videos");
      return;
    }

    createRelationship({
      parentId,
      relationships: selectedVideos.map((v) => ({
        childId: v.video.id,
        relationType: v.relationType,
      })),
    });
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild disabled={disabled}>
        {trigger ?? (
          <Button variant="primary" size="sm" disabled={disabled}>
            Link Videos
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="border-0 bg-gradient-to-br from-dark to-grey shadow-2xl backdrop-blur-sm sm:max-w-[700px] sm:rounded-2xl">
        <DialogHeader className="space-y-4 pb-6">
          <div className="flex items-center gap-3">
            <div className="flex h-10 w-10 items-center justify-center rounded-full bg-orange/20">
              <Link className="h-5 w-5 text-orange" />
            </div>
            <div>
              <DialogTitle className="text-2xl font-bold text-white">
                Link Videos
              </DialogTitle>
              <DialogDescription className="text-muted-foreground">
                Connect this video to related content with synchronized playback
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <div className="space-y-6">
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Label className="text-base font-semibold text-white">
                Select Videos to Link
              </Label>
              {currentVideoSport && (
                <span className="rounded-full bg-orange/20 px-3 py-1 text-xs font-medium text-orange">
                  {formatString(currentVideoSport)}
                </span>
              )}
            </div>

            {isLoadingVideosBySport ? (
              <div className="flex items-center gap-3 rounded-lg bg-muted/50 p-4">
                <div className="h-4 w-4 animate-spin rounded-full border-2 border-orange border-t-transparent"></div>
                <span className="text-sm text-muted-foreground">
                  Loading videos...
                </span>
              </div>
            ) : currentVideoSport ? (
              <div className="rounded-lg border border-border/50 bg-card/50 p-4">
                <MultiSelect
                  videos={availableVideos.map((video) => {
                    const selectedVideo = selectedVideos.find(
                      (sv) => sv.video.id === video.id,
                    );
                    return {
                      ...video,
                      relationType: selectedVideo?.relationType,
                    };
                  })}
                  selectedVideos={selectedVideos.map((sv) => sv.video)}
                  onSelectionChange={handleVideoSelection}
                  placeholder={`Select videos from ${formatString(currentVideoSport)}...`}
                />
              </div>
            ) : (
              <div className="rounded-lg border border-destructive/20 bg-destructive/5 p-4">
                <div className="flex items-center gap-2 text-sm text-destructive">
                  <svg
                    className="h-4 w-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                    />
                  </svg>
                  Unable to load video sport
                </div>
              </div>
            )}

            {availableVideos.length === 0 && currentVideoSport && (
              <div className="rounded-lg border border-muted/20 bg-muted/10 p-4">
                <p className="text-sm text-muted-foreground">
                  No other videos found in{" "}
                  <span className="font-medium text-orange">
                    {currentVideoSport}
                  </span>{" "}
                  sport.
                </p>
              </div>
            )}
          </div>

          {selectedVideos.length > 0 && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Label className="text-base font-semibold text-white">
                  Configure Relationships
                </Label>
                <span className="text-xs text-muted-foreground">
                  {selectedVideos.length} video
                  {selectedVideos.length !== 1 ? "s" : ""} selected
                </span>
              </div>

              <div className="max-h-64 space-y-3 overflow-y-auto rounded-lg border border-border/50 bg-card/30 p-4">
                {selectedVideos.map((item, index) => (
                  <div
                    key={item.video.id}
                    className="group relative rounded-lg border border-border/30 bg-gradient-to-r from-card/50 to-card/30 p-4 transition-all hover:border-orange/30 hover:shadow-lg"
                  >
                    <div className="flex items-center gap-4">
                      <div className="flex h-8 w-8 items-center justify-center rounded-full bg-orange/20 text-xs font-bold text-orange">
                        {index + 1}
                      </div>

                      <div className="min-w-0 flex-1">
                        <div className="truncate text-sm font-medium text-white">
                          {item.video.title || item.video.filename}
                        </div>
                        <div className="text-xs text-muted-foreground">
                          ID: {item.video.id}
                        </div>
                      </div>

                      <div className="max-w-xs flex-1">
                        <Input
                          value={item.relationType}
                          onChange={(e) =>
                            handleRelationTypeChange(
                              item.video.id,
                              e.target.value,
                            )
                          }
                          placeholder="e.g., sync, duplicate, alternate"
                          className="border-border/50 bg-background/50 text-white placeholder:text-muted-foreground focus:border-orange focus:ring-1 focus:ring-orange/20"
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          <DialogFooter className="flex gap-3 pt-6">
            <Button
              type="button"
              variant="secondary"
              onClick={() => {
                setOpen(false);
                setSelectedVideos([]);
              }}
              disabled={isPending}
              className="flex-1"
            >
              Cancel
            </Button>
            <Button
              onClick={onSubmit}
              disabled={isPending || selectedVideos.length === 0}
              variant="primary"
              className="flex-1"
            >
              {isPending ? (
                <div className="flex items-center gap-2">
                  <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                  Linking...
                </div>
              ) : (
                `Link ${selectedVideos.length} Video${selectedVideos.length !== 1 ? "s" : ""}`
              )}
            </Button>
          </DialogFooter>
        </div>
      </DialogContent>
    </Dialog>
  );
};
